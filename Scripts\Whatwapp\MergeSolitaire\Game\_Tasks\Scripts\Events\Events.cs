
using System.Collections.Generic;
using _Tasks.NewFeature;
using UnityEngine;
using Whatwapp.MergeSolitaire.Game;

namespace _Tasks.Events
{
    public struct StartGameEvent
    {
        
    }

    public struct MergeBlockEvent
    {
        public List<List<Cell>> MergeableGroups;
        public GameController GameController;
        public BlockFactory BlockFactory;
        public FoundationsController FoundationsController;
        public System.Action OnComplete;
    }
    
    public struct BombPlacedEvent
    {
        public BombBlock BombBlock;
        public Vector2Int Position;
        public float ExplosionDelay;
    }

    public struct BombMovementStoppedEvent
    {
        public BombBlock BombBlock;
        public Vector2Int FinalPosition;
        public List<Cell> AffectedCells;
    }

    public struct BombExplosionEvent
    {
        public Vector3 ExplosionCenter;
        public List<Block> DestroyedBlocks;
        public int ExplosionRadius;
        public BombBlock SourceBomb;
    }

    public struct BlockDestructionRequestEvent
    {
        public List<Block> BlocksToDestroy;
        public Vector3 ExplosionCenter;
        public bool PlayDestructionEffects;
    }

    public struct ExplosionEffectEvent
    {
        public Vector3 Position;
        public int Radius;
        public float Intensity;
    }

    /// <summary>
    /// Event broadcasted when blocks have been destroyed and the board needs to be updated
    /// This should trigger block movement and merging checks
    /// </summary>
    public struct BlocksDestroyedEvent
    {
        public int DestroyedBlockCount;
        public Vector3 DestructionCenter;
        public bool RequiresMovement;
        public bool RequiresMergeCheck;
    }

    /// <summary>
    /// Event triggered when blocks are marked for destruction by bomb explosion
    /// Contains positions for VFX synchronization
    /// </summary>
    public struct BombDestructionMarkedEvent
    {
        public List<Vector3> BlockPositions;
        public Vector3 ExplosionCenter;
        public int ExplosionRadius;
        public float ExpectedDestructionDelay;
    }

    /// <summary>
    /// Event triggered when VFX effects should be synchronized with actual block destruction
    /// </summary>
    public struct VFXSyncEvent
    {
        public List<Vector3> EffectPositions;
        public bool TriggerSubExplosions;
        public bool TriggerScreenShake;
    }

    /// <summary>
    /// Event triggered when score is updated to play particle effects
    /// </summary>
    public struct ScoreUpdateEvent
    {
        public int PreviousScore;
        public int NewScore;
        public Vector3 ScorePosition;
        public bool IsAnimated;
        public bool HasMilestone; // True if this score update includes a milestone crossing
    }

    /// <summary>
    /// Event triggered when a score milestone is achieved
    /// </summary>
    public struct ScoreMilestoneEvent
    {
        public int MilestoneScore;
        public Vector3 ScorePosition;
        public int MilestoneLevel; // 1000, 2000, 3000, etc.
    }

    /// <summary>
    /// Event triggered when blocks arrive at their destination for impactful effects
    /// </summary>
    public struct BlockArrivalEvent
    {
        public Vector3 ArrivalPosition;
        public Block ArrivedBlock;
        public bool IsSpecialBlock;
        public float ImpactIntensity; // 0.0 to 1.0
        public bool TriggerScreenShake;
        public bool TriggerParticles;
        public bool IsFromMerge; // True if this arrival is from a merge operation
    }

    /// <summary>
    /// Event triggered when the game ends in victory
    /// Contains all data needed for victory animation sequences
    /// </summary>
    public struct GameVictoryEvent
    {
        public int FinalScore;
        public int PreviousHighScore;
        public bool IsNewHighScore;
        public int VictoryBonus;
        public float CompletionTime;
        public int MilestonesAchieved;
        public Vector3 ScorePosition; // Position for score-related effects
        public bool PlayCelebrationParticles;
        public bool PlayVictorySound;
        public string VictoryMessage; // Custom victory message if any
    }

    /// <summary>
    /// Event triggered when the game ends in defeat
    /// Contains all data needed for defeat animation sequences
    /// </summary>
    public struct GameDefeatEvent
    {
        public int FinalScore;
        public int HighScore;
        public float SurvivalTime;
        public int BlocksPlaced;
        public int LastMilestone;
        public Vector3 ScorePosition; // Position for score-related effects
        public bool PlayDefeatParticles;
        public bool PlayDefeatSound;
        public string DefeatReason; // Reason for game over (e.g., "No more moves", "Board full")
        public bool ShowEncouragement; // Whether to show encouraging message
    }

    /// <summary>
    /// Event triggered to start result animation sequence
    /// Used by animation controller to coordinate all result animations
    /// </summary>
    public struct ResultAnimationStartEvent
    {
        public bool IsVictory;
        public int FinalScore;
        public bool IsNewHighScore;
        public Vector3 ScorePosition;
        public float AnimationDuration; // Total duration for the animation sequence
        public bool SkipToEnd; // For testing or user preference
    }

    /// <summary>
    /// Event triggered when result animation sequence completes
    /// Signals that it's safe to transition to end game scene
    /// </summary>
    public struct ResultAnimationCompleteEvent
    {
        public bool IsVictory;
        public float TotalAnimationTime;
        public bool UserSkipped; // True if user skipped the animation
    }

    // Scene transition events removed - using direct method calls for simplicity
}