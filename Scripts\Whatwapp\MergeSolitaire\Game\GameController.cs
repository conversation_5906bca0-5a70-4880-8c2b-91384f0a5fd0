using System;
using _Tasks.Events;
using DG.Tweening;
using UnityEngine;
using Whatwapp.Core.Cameras;
using Whatwapp.Core.FSM;
using Whatwapp.Core.Audio;
using Whatwapp.MergeSolitaire.Game.GameStates;
using Whatwapp.MergeSolitaire.Game.UI;

namespace Whatwapp.MergeSolitaire.Game
{
    public class GameController : MonoBehaviour
    {
        [Header("References")] 
        [SerializeField] private Board board;
        [SerializeField] private GridBuilder _gridBuilder;
        [SerializeField] private TargetBoundedOrthographicCamera _targetBoundedCamera;
        [SerializeField] private BlockFactory _blockFactory;
        [SerializeField] private NextBlockController _nextBlockController;
        [SerializeField] private FoundationsController _foundationsController;
        
        [SerializeField] private ScoreBox _scoreBox;

        [Header("Settings")]
        [SerializeField] private AnimationSettings _animationSettings;

        private StateMachine _stateMachine;
        private SFXManager _sfxManager;

        private int _score = 0;
        private int _highScore = 0;
        private bool _isStarted = false;
        private bool _isPaused = false;

        // State references for event-driven transitions
        private MoveBlocksState _moveBlocksState;
        private MergeBlocksState _mergeBlocksState;
        private VFXState _vfxState;
        private bool _pendingVFXProcessing = false;
        
        public bool IsPaused
        {
            get => _isPaused;
            set
            {
                _isPaused = value;
            }
        }

        public int Score
        {
            get => _score;
            set
            {
                _score = value;
                if (_score > _highScore)
                {
                    _highScore = _score;
                    PlayerPrefs.SetInt(Consts.PREFS_HIGHSCORE, _highScore);
                }
                _scoreBox.SetScore(_score);
                PlayerPrefs.SetInt(Consts.PREFS_LAST_SCORE, _score);
            }
        }

        private void Awake()
        {
            EventManager.Subscribe<StartGameEvent>(OnStartGame);
        }

        private void OnStartGame(StartGameEvent startGameEvent)
        {
            if (!_isStarted)
                enabled = true;
        }
        
        private void Start()
        {
            DOTween.Init(true, false, LogBehaviour.Verbose).SetCapacity(500, 250);
            
            _stateMachine = new StateMachine();
            _sfxManager = SFXManager.Instance;

            var generateLevel = new GenerateLevelState(this, board, _gridBuilder, _blockFactory, _targetBoundedCamera);
            var extractBlock = new ExtractBlockState(this, _nextBlockController, _sfxManager);
            _moveBlocksState = new MoveBlocksState(this, board, _animationSettings);
            _mergeBlocksState = new MergeBlocksState(this, board, _blockFactory, _foundationsController);
            _vfxState = new VFXState(this);
            var playBlockState = new PlayBlockState(this, board, _nextBlockController, _sfxManager);
            var gameOver = new GameOverState(this,  _sfxManager);
            var victory = new VictoryState(this,  _sfxManager);

            // Subscribe to bomb-related events
            EventManager.Subscribe<BlocksDestroyedEvent>(OnBlocksDestroyed);


            _stateMachine.AddTransition(generateLevel, extractBlock, 
                new Predicate(() => _gridBuilder.IsReady()));
            
            _stateMachine.AddTransition(extractBlock, _moveBlocksState,
                new Predicate(() => extractBlock.ExtractCompleted));

            _stateMachine.AddTransition(_moveBlocksState, _mergeBlocksState,
                new Predicate(() => _moveBlocksState.CanMoveBlocks() == false));

            _stateMachine.AddTransition(_mergeBlocksState, victory,
                new Predicate(() => _foundationsController.AllFoundationsCompleted));
            _stateMachine.AddTransition(_mergeBlocksState, _vfxState,
                new Predicate(() =>
                    _mergeBlocksState.MergeCompleted && _mergeBlocksState.MergeCount > 0
                                               && ! _foundationsController.AllFoundationsCompleted && _pendingVFXProcessing));
            _stateMachine.AddTransition(_mergeBlocksState, _vfxState,
                new Predicate(() =>
                    _mergeBlocksState.MergeCompleted && _mergeBlocksState.MergeCount == 0
                                               && ! _foundationsController.AllFoundationsCompleted && _pendingVFXProcessing));
            _stateMachine.AddTransition(_mergeBlocksState, _moveBlocksState,
                new Predicate(() =>
                    _mergeBlocksState.MergeCompleted && _mergeBlocksState.MergeCount > 0
                                               && ! _foundationsController.AllFoundationsCompleted && !_pendingVFXProcessing));
            _stateMachine.AddTransition(_mergeBlocksState, playBlockState,
                new Predicate(() => _mergeBlocksState.MergeCompleted && _mergeBlocksState.MergeCount == 0
                                                               && ! _foundationsController.AllFoundationsCompleted && !_pendingVFXProcessing));

            // VFX State transitions
            _stateMachine.AddTransition(_vfxState, _moveBlocksState,
                new Predicate(() => _vfxState.CanTransition() && _moveBlocksState.CanMoveBlocks()));
            _stateMachine.AddTransition(_vfxState, playBlockState,
                new Predicate(() => _vfxState.CanTransition() && !_moveBlocksState.CanMoveBlocks()));

            _stateMachine.AddTransition(playBlockState, extractBlock,
                new Predicate(() =>  playBlockState.PlayBlockCompleted));
            _stateMachine.AddTransition(playBlockState, gameOver,
                new Predicate(() => playBlockState.GameOver));
            // Transition directly from PlayBlockState to VFXState when VFX processing is requested
            _stateMachine.AddTransition(playBlockState, _vfxState,
                new Predicate(() => _pendingVFXProcessing));
            
            
            _stateMachine.SetState(generateLevel);
            
            _highScore = PlayerPrefs.GetInt(Consts.PREFS_HIGHSCORE, 0);
            Score = 0;
            _isStarted = true;
        }

        private void Update()
        {
            _stateMachine.Update();
        }

        private void FixedUpdate()
        {
            _stateMachine.FixedUpdate();
        }

        private void OnDestroy()
        {
            // Unsubscribe from events to prevent memory leaks
            EventManager.Unsubscribe<BlocksDestroyedEvent>(OnBlocksDestroyed);
        }

        /// <summary>
        /// Event handler for when blocks are destroyed (e.g., by bomb explosion)
        /// Sets flag for VFX processing - state machine will transition naturally when current state completes
        /// </summary>
        private void OnBlocksDestroyed(BlocksDestroyedEvent eventData)
        {
            Debug.Log($"[GameController] Blocks destroyed event received. Count: {eventData.DestroyedBlockCount}");

            // Only trigger VFX processing if blocks were actually destroyed
            if (eventData.DestroyedBlockCount > 0)
            {
                // Set flag to indicate VFX processing is needed
                // The state machine will naturally transition to VFXState when MergeBlocksState completes
                _pendingVFXProcessing = true;

                Debug.Log("[GameController] VFX processing flag set - will transition to VFXState when current state completes");
            }
        }

        /// <summary>
        /// Reset the VFX processing flag - called by VFXState when processing is complete
        /// </summary>
        public void ResetVFXProcessing()
        {
            _pendingVFXProcessing = false;
            Debug.Log("[GameController] VFX processing flag reset");
        }
    }
}