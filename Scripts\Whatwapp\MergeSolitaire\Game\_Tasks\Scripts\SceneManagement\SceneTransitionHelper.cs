using UnityEngine;
using Whatwapp.MergeSolitaire.Game;
using Whatwapp.Core.Utils;

namespace _Tasks.NewFeature.SceneManagement
{
    /// <summary>
    /// Simple helper for scene transitions
    /// </summary>
    public static class SceneTransitionHelper
    {
        public static void GoToMainMenu(float duration = 1f, System.Action onComplete = null)
        {
            TransitionToScene(Consts.SCENE_MAIN_MENU, duration,onComplete: onComplete);
        }

        public static void GoToGame(float duration = 1f, System.Action onComplete = null)
        {
            TransitionToScene(Consts.SCENE_GAME, duration,onComplete: onComplete);
        }

        public static void GoToEndGame(float duration = 1f, System.Action onComplete = null)
        {
            TransitionToScene(Consts.SCENE_END_GAME, duration,onComplete: onComplete);
        }

        /// <summary>
        /// Load Pause Menu additively
        /// </summary>
        public static void ShowPauseMenu(float duration = 0.5f, System.Action onComplete = null)
        {
            if (SceneTransitionServiceLocator.IsAvailable)
            {
                // Load pause menu additively WITHOUT unloading current scene (keep game running underneath)
                SceneTransitionServiceLocator.Instance.LoadSceneAdditive("PauseMenu", duration, false,onComplete);
            }
            else
            {
                // Fallback to direct additive loading
                UnityEngine.SceneManagement.SceneManager.LoadSceneAsync("PauseMenu", UnityEngine.SceneManagement.LoadSceneMode.Additive);
                onComplete?.Invoke();
            }
        }

        /// <summary>
        /// Unload Pause Menu with transition effects
        /// </summary>
        public static void HidePauseMenu(float duration = 0.5f, System.Action onComplete = null)
        {
            if (SceneTransitionServiceLocator.IsAvailable)
            {
                // Unload pause menu with transition effects
                SceneTransitionServiceLocator.Instance.UnloadScene("PauseMenu", duration, onComplete:onComplete);
            }
            else
            {
                // Fallback to direct unloading
                UnityEngine.SceneManagement.SceneManager.UnloadSceneAsync("PauseMenu");
                onComplete?.Invoke();
            }
        }

        /// <summary>
        /// Core method for scene transitions
        /// </summary>
        public static void VictoryToEndGame(System.Action onComplete = null)
        {
            GoToEndGame(1.5f, onComplete);
        }

        public static void DefeatToEndGame(System.Action onComplete = null)
        {
            GoToEndGame(1f, onComplete);
        }

        /// <summary>
        /// General method for transitioning to any scene by name
        /// </summary>
        public static void TransitionToScene(string sceneName, float duration = 1f, bool showLoadingPanel = true, System.Action onComplete = null)
        {
            if (SceneTransitionServiceLocator.IsAvailable)
            {
                SceneTransitionServiceLocator.Instance.TransitionToScene(sceneName, duration, onComplete);
            }
            else
            {
                UnityEngine.SceneManagement.SceneManager.LoadScene(sceneName);
                onComplete?.Invoke();
            }
        }

        /// <summary>
        /// Unload a scene with transition effects (public version)
        /// </summary>
        public static void UnloadSceneWithTransition(string sceneName, float duration = 0.5f, bool showLoading = true, bool showLoadingPanel = true, System.Action onComplete = null)
        {
            if (SceneTransitionServiceLocator.IsAvailable)
            {
                SceneTransitionServiceLocator.Instance.UnloadScene(sceneName, duration, onComplete);
            }
            else
            {
                UnityEngine.SceneManagement.SceneManager.UnloadSceneAsync(sceneName);
                onComplete?.Invoke();
            }
        }
    }
}
