using System.Collections;
using _Tasks.Events;
using UnityEngine;
using UnityEngine.SceneManagement;
using DG.Tweening;

namespace _Tasks.NewFeature.SceneManagement
{
    /// <summary>
    /// Simple scene transition manager with fade effects
    /// </summary>
    public class SceneTransitionManager : MonoBehaviour
    {
        [Header("Settings")] [SerializeField] private float _transitionDuration = 1f;
        [SerializeField] private Color _fadeColor = Color.black;

        [Header("UI References")] [SerializeField]
        private CanvasGroup _canvasGroup;

        [SerializeField] private UnityEngine.UI.Image _fadeOverlay;

        private static SceneTransitionManager _instance;
        public static SceneTransitionManager Instance => _instance;

        public bool IsTransitioning { get; private set; }
        public string CurrentSceneName { get; private set; }

        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
                Initialize();
            }
            else
            {
                Destroy(gameObject);
            }
            
            SceneManager.activeSceneChanged += SceneManagerOnactiveSceneChanged;

            void SceneManagerOnactiveSceneChanged(Scene oldScene, Scene newScene)
            {
                if (newScene.name == "Game")
                {
                    EventManager.Broadcast<StartGameEvent>();
                }
            }
        }

        private void Start()
        {
            CurrentSceneName = SceneManager.GetActiveScene().name;

            // Ensure service is registered
            var service = GetComponent<SceneTransitionService>();
            if (service == null)
            {
                gameObject.AddComponent<SceneTransitionService>();
            }
        }

        private void Initialize()
        {
            if (_canvasGroup != null)
            {
                _canvasGroup.alpha = 0f;
                _canvasGroup.blocksRaycasts = false;
                _canvasGroup.interactable = false;
            }

            if (_fadeOverlay != null)
            {
                _fadeOverlay.color = new Color(_fadeColor.r, _fadeColor.g, _fadeColor.b, 0f);
            }

        }

        /// <summary>
        /// Simple scene transition with fade
        /// </summary>
        public void TransitionToScene(string sceneName, float duration = 0f,
            System.Action onComplete = null)
        {
            if (IsTransitioning) return;

            float actualDuration = duration > 0 ? duration : _transitionDuration;
            StartCoroutine(TransitionCoroutine(sceneName, actualDuration, onComplete));
        }

        /// <summary>
        /// Load scene additively with optional current scene unload
        /// </summary>
        public void LoadSceneAdditiveWithUnload(string sceneName, float duration = 0f, System.Action onComplete = null)
        {
            if (IsTransitioning) return;

            float actualDuration = duration > 0 ? duration : _transitionDuration;
            StartCoroutine(AdditiveTransitionCoroutine(sceneName, actualDuration, true, onComplete));
        }

        /// <summary>
        /// Load scene additively without unloading current scene
        /// </summary>
        public void LoadSceneAdditive(string sceneName, float duration = 0f, System.Action onComplete = null)
        {
            if (IsTransitioning) return;

            float actualDuration = duration > 0 ? duration : _transitionDuration;
            StartCoroutine(AdditiveTransitionCoroutine(sceneName, actualDuration, false, onComplete));
        }

        /// <summary>
        /// Unload scene with transition effects
        /// </summary>
        public void UnloadSceneWithTransition(string sceneName, float duration = 0f,  System.Action onComplete = null)
        {
            if (IsTransitioning) return;

            float actualDuration = duration > 0 ? duration : _transitionDuration;
            StartCoroutine(UnloadTransitionCoroutine(sceneName, actualDuration
                , onComplete));
        }

        private IEnumerator TransitionCoroutine(string sceneName, float duration,
            System.Action onComplete)
        {
            IsTransitioning = true;
            string previousScene = CurrentSceneName;

            // Fade out
            yield return FadeOut(duration * 0.3f);


            // Load scene
            var loadOperation = SceneManager.LoadSceneAsync(sceneName);
            yield return loadOperation;

            // Update current scene
            CurrentSceneName = sceneName;

            // Fade in
            yield return FadeIn(duration * 0.3f);

            IsTransitioning = false;
            onComplete?.Invoke();
        }

        private IEnumerator AdditiveTransitionCoroutine(string sceneName, float duration, bool unloadCurrent,
            System.Action onComplete)
        {
            IsTransitioning = true;
            string previousScene = CurrentSceneName;

            // Fade out
            yield return FadeOut(duration * 0.3f);

            // Load scene additively
            var loadOperation = SceneManager.LoadSceneAsync(sceneName, LoadSceneMode.Additive);
            yield return loadOperation;

            // Set new scene as active
            var newScene = SceneManager.GetSceneByName(sceneName);
            if (newScene.IsValid())
            {
                SceneManager.SetActiveScene(newScene);
                CurrentSceneName = sceneName;
            }

            // Unload previous scene if requested
            if (unloadCurrent && !string.IsNullOrEmpty(previousScene) && previousScene != sceneName)
            {
                var unloadOperation = SceneManager.UnloadSceneAsync(previousScene);
                yield return unloadOperation;
            }

            // Fade in
            yield return FadeIn(duration * 0.3f);

            IsTransitioning = false;
            onComplete?.Invoke();
        }

        private IEnumerator UnloadTransitionCoroutine(string sceneName, float duration, System.Action onComplete)
        {
            IsTransitioning = true;

            // Fade out
            yield return FadeOut(duration * 0.3f);

            // Unload scene
            var unloadOperation = SceneManager.UnloadSceneAsync(sceneName);
            yield return unloadOperation;

            // Fade in
            yield return FadeIn(duration * 0.3f);

            IsTransitioning = false;
            onComplete?.Invoke();
        }

        private IEnumerator FadeOut(float duration)
        {
            if (_canvasGroup != null)
            {
                _canvasGroup.blocksRaycasts = true;
                _canvasGroup.interactable = true;
                yield return _canvasGroup.DOFade(1f, duration).WaitForCompletion();
            }

            if (_fadeOverlay != null)
            {
                Color targetColor = new Color(_fadeColor.r, _fadeColor.g, _fadeColor.b, 1f);
                yield return _fadeOverlay.DOColor(targetColor, duration).WaitForCompletion();
            }
        }

        private IEnumerator FadeIn(float duration)
        {
            if (_fadeOverlay != null)
            {
                Color targetColor = new Color(_fadeColor.r, _fadeColor.g, _fadeColor.b, 0f);
                yield return _fadeOverlay.DOColor(targetColor, duration).WaitForCompletion();
            }

            if (_canvasGroup != null)
            {
                yield return _canvasGroup.DOFade(0f, duration).WaitForCompletion();
                _canvasGroup.blocksRaycasts = false;
                _canvasGroup.interactable = false;
            }
        }
    }
}