using UnityEngine;
using _Tasks.NewFeature.SceneManagement;

namespace Whatwapp.MergeSolitaire.Game.UI
{
    /// <summary>
    /// UI Action that handles pause menu showing/hiding using SceneTransitionHelper
    /// </summary>
    public class PauseMenuAction : MonoBehaviour
    {
        [Header("Settings")]
        [SerializeField] private float _transitionDuration = 0.5f;
        
        /// <summary>
        /// Show the pause menu (loads additively without unloading current scene)
        /// </summary>
        public void ShowPauseMenu()
        {
            // Pause the game
            var gameController = FindObjectOfType<GameController>();
            if (gameController != null)
            {
                gameController.IsPaused = true;
            }
            
            // Show pause menu with transition
            SceneTransitionHelper.ShowPauseMenu(_transitionDuration);
        }
        
        /// <summary>
        /// Hide the pause menu and resume game
        /// </summary>
        public void HidePauseMenu()
        {
            // Hide pause menu with transition
            SceneTransitionHelper.HidePauseMenu(_transitionDuration, () =>
            {
                // Resume the game after transition completes
                var gameController = FindObjectOfType<GameController>();
                if (gameController != null)
                {
                    gameController.IsPaused = false;
                }
            });
        }
        
        /// <summary>
        /// Toggle pause menu state
        /// </summary>
        public void TogglePauseMenu()
        {
            var gameController = FindObjectOfType<GameController>();
            if (gameController != null)
            {
                if (gameController.IsPaused)
                {
                    HidePauseMenu();
                }
                else
                {
                    ShowPauseMenu();
                }
            }
        }
    }
}
