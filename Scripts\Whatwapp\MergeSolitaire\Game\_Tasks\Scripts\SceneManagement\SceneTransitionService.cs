using UnityEngine;
using Whatwapp.Core.Utils;

namespace _Tasks.NewFeature.SceneManagement
{
    /// <summary>
    /// Simple service that bridges the interface with the transition manager
    /// </summary>
    public class SceneTransitionService : MonoBehaviour, ISceneTransitionService
    {
        private SceneTransitionManager _transitionManager;

        private void Awake()
        {
            _transitionManager = GetComponent<SceneTransitionManager>();
            if (_transitionManager == null)
            {
                _transitionManager = SceneTransitionManager.Instance;
            }

            SceneTransitionServiceLocator.Register(this);
        }

        private void OnDestroy()
        {
            if (SceneTransitionServiceLocator.Instance == this)
            {
                SceneTransitionServiceLocator.Unregister();
            }
        }

        public bool IsAvailable => _transitionManager != null;
        public bool IsTransitioning => _transitionManager != null && _transitionManager.IsTransitioning;

        public void TransitionToScene(string sceneName)
        {
            if (_transitionManager != null)
            {
                _transitionManager.TransitionToScene(sceneName);
            }
            else
            {
                UnityEngine.SceneManagement.SceneManager.LoadScene(sceneName);
            }
        }

        public void TransitionToScene(string sceneName, float duration, System.Action onComplete = null)
        {
            if (_transitionManager != null)
            {
                _transitionManager.TransitionToScene(sceneName, duration, onComplete);
            }
            else
            {
                UnityEngine.SceneManagement.SceneManager.LoadScene(sceneName);
                onComplete?.Invoke();
            }
        }

        public void LoadSceneAdditive(string sceneName, float duration = 0.5f, bool unloadCurrent = false, System.Action onComplete = null)
        {
            if (_transitionManager != null)
            {
                if (unloadCurrent)
                {
                    _transitionManager.LoadSceneAdditiveWithUnload(sceneName, duration, onComplete);
                }
                else
                {
                    _transitionManager.LoadSceneAdditive(sceneName, duration, onComplete);
                }
            }
            else
            {
                if (unloadCurrent)
                {
                    StartCoroutine(FallbackAdditiveWithUnload(sceneName, onComplete));
                }
                else
                {
                    UnityEngine.SceneManagement.SceneManager.LoadSceneAsync(sceneName, UnityEngine.SceneManagement.LoadSceneMode.Additive);
                    onComplete?.Invoke();
                }
            }
        }

        private System.Collections.IEnumerator FallbackAdditiveWithUnload(string sceneName, System.Action onComplete)
        {
            string currentScene = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;

            var loadOperation = UnityEngine.SceneManagement.SceneManager.LoadSceneAsync(sceneName, UnityEngine.SceneManagement.LoadSceneMode.Additive);
            yield return loadOperation;

            var newScene = UnityEngine.SceneManagement.SceneManager.GetSceneByName(sceneName);
            if (newScene.IsValid())
            {
                UnityEngine.SceneManagement.SceneManager.SetActiveScene(newScene);
            }

            if (!string.IsNullOrEmpty(currentScene) && currentScene != sceneName)
            {
                yield return UnityEngine.SceneManagement.SceneManager.UnloadSceneAsync(currentScene);
            }

            onComplete?.Invoke();
        }

        public void UnloadScene(string sceneName, float duration = 0.5f, System.Action onComplete = null)
        {
            if (_transitionManager != null)
            {
                _transitionManager.UnloadSceneWithTransition(sceneName, duration, onComplete);
            }
            else
            {
                // Fallback to direct scene unloading
                var asyncOperation = UnityEngine.SceneManagement.SceneManager.UnloadSceneAsync(sceneName);
                if (asyncOperation != null && onComplete != null)
                {
                    asyncOperation.completed += _ => onComplete.Invoke();
                }
                else
                {
                    onComplete?.Invoke();
                }
            }
        }

        public void ForceCompleteTransition()
        {
            // Simple implementation - just stop any ongoing transitions
            if (_transitionManager != null && _transitionManager.IsTransitioning)
            {
                // Force complete by stopping all coroutines on the manager
                _transitionManager.StopAllCoroutines();
            }
        }
    }
}
