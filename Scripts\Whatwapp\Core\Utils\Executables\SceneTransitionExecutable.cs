using UnityEngine;

namespace Whatwapp.Core.Utils.Executables
{
    /// <summary>
    /// Executable that uses the scene transition service for smooth scene changes
    /// Can be used in UI buttons and other game events
    /// </summary>
    public class SceneTransitionExecutable : MonoBehaviour, IExecutable
    {
        [Header("Transition Settings")]
        [SerializeField] private string _targetScene;
        [SerializeField] private float _transitionDuration = 1f;
        [SerializeField] private bool _useAdditiveLoading = false;

        [Header("Predefined Transitions")]
        [SerializeField] private bool _usePredefinedTransition = false;
        [SerializeField] private PredefinedTransition _predefinedTransition = PredefinedTransition.MainMenu;

        public enum PredefinedTransition
        {
            MainMenu,
            Game,
            EndGame,
            PauseMenu,
            ReloadCurrent
        }

        public void Execute()
        {
            if (_usePredefinedTransition)
            {
                ExecutePredefinedTransition();
            }
            else
            {
                ExecuteCustomTransition();
            }
        }

        private void ExecuteCustomTransition()
        {
            if (SceneTransitionServiceLocator.IsAvailable)
            {
                var service = SceneTransitionServiceLocator.Instance;
                
                if (_useAdditiveLoading)
                {
                    service.LoadSceneAdditive(_targetScene, _transitionDuration);
                }
                else
                {
                    service.TransitionToScene(_targetScene, _transitionDuration);
                }
            }
            else
            {
                // Fallback to direct scene loading
                UnityEngine.SceneManagement.SceneManager.LoadScene(_targetScene,
                    _useAdditiveLoading ? UnityEngine.SceneManagement.LoadSceneMode.Additive :
                        UnityEngine.SceneManagement.LoadSceneMode.Single);
            }
        }

        private void ExecutePredefinedTransition()
        {
            if (!SceneTransitionServiceLocator.IsAvailable)
            {
                // Fallback for predefined transitions
                string sceneName = GetSceneNameForPredefinedTransition();
                if (!string.IsNullOrEmpty(sceneName))
                {
                    UnityEngine.SceneManagement.SceneManager.LoadScene(sceneName);
                }
                return;
            }

            var service = SceneTransitionServiceLocator.Instance;

            switch (_predefinedTransition)
            {
                case PredefinedTransition.MainMenu:
                    service.TransitionToScene("MainMenu", _transitionDuration);
                    break;
                case PredefinedTransition.Game:
                    service.TransitionToScene("Game", _transitionDuration);
                    break;
                case PredefinedTransition.EndGame:
                    service.TransitionToScene("EndGame", _transitionDuration);
                    break;
                case PredefinedTransition.PauseMenu:
                    service.LoadSceneAdditive("PauseMenu", _transitionDuration);
                    break;
                case PredefinedTransition.ReloadCurrent:
                    string currentScene = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
                    service.TransitionToScene(currentScene, _transitionDuration);
                    break;
            }
        }

        private string GetSceneNameForPredefinedTransition()
        {
            switch (_predefinedTransition)
            {
                case PredefinedTransition.MainMenu: return "MainMenu";
                case PredefinedTransition.Game: return "Game";
                case PredefinedTransition.EndGame: return "EndGame";
                case PredefinedTransition.PauseMenu: return "PauseMenu";
                case PredefinedTransition.ReloadCurrent: 
                    return UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
                default: return null;
            }
        }

        // Quick methods for UI buttons
        public void GoToMainMenu() => ExecutePredefinedTransition(PredefinedTransition.MainMenu);
        public void GoToGame() => ExecutePredefinedTransition(PredefinedTransition.Game);
        public void GoToEndGame() => ExecutePredefinedTransition(PredefinedTransition.EndGame);
        public void ShowPauseMenu() => ExecutePredefinedTransition(PredefinedTransition.PauseMenu);
        public void ReloadScene() => ExecutePredefinedTransition(PredefinedTransition.ReloadCurrent);

        private void ExecutePredefinedTransition(PredefinedTransition transition)
        {
            _predefinedTransition = transition;
            _usePredefinedTransition = true;
            ExecutePredefinedTransition();
        }
    }
}
