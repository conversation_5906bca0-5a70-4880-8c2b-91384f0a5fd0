using UnityEngine;
using _Tasks.NewFeature.SceneManagement;

namespace Whatwapp.MergeSolitaire.Game.UI
{
    /// <summary>
    /// UI Action that uses SceneTransitionHelper for scene loading
    /// This replaces the old LoadSceneAction to use the new transition system
    /// </summary>
    public class LoadSceneAction : MonoBehaviour
    {
        [Header("Scene Settings")]
        [SerializeField] private string _targetScene = "MainMenu";
        [SerializeField] private float _transitionDuration = 1f;
        
        [Header("Predefined Scenes")]
        [SerializeField] private bool _usePredefinedScene = true;
        [SerializeField] private PredefinedScene _predefinedScene = PredefinedScene.MainMenu;
        
        public enum PredefinedScene
        {
            MainMenu,
            Game,
            EndGame
        }
        
        /// <summary>
        /// Execute the scene transition
        /// </summary>
        public void Execute()
        {
            if (_usePredefinedScene)
            {
                ExecutePredefinedTransition();
            }
            else
            {
                // Use custom scene name
                SceneTransitionHelper.TransitionToScene(_targetScene, _transitionDuration, false);
            }
        }
        
        private void ExecutePredefinedTransition()
        {
            switch (_predefinedScene)
            {
                case PredefinedScene.MainMenu:
                    SceneTransitionHelper.GoToMainMenu(_transitionDuration);
                    break;
                case PredefinedScene.Game:
                    SceneTransitionHelper.GoToGame(_transitionDuration);
                    break;
                case PredefinedScene.EndGame:
                    SceneTransitionHelper.GoToEndGame(_transitionDuration);
                    break;
            }
        }
        
        // Quick methods for UI buttons
        public void GoToMainMenu() => SceneTransitionHelper.GoToMainMenu(_transitionDuration);
        public void GoToGame() => SceneTransitionHelper.GoToGame(_transitionDuration);
        public void GoToEndGame() => SceneTransitionHelper.GoToEndGame(_transitionDuration);
    }
}
