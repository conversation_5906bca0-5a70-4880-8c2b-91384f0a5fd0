using System;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using Whatwapp.MergeSolitaire.Game;
using Whatwapp.MergeSolitaire.Game.UI;
using Whatwapp.Core.Audio;
using _Tasks.NewFeature.Settings;
using _Tasks.NewFeature.Shared;
using Whatwapp.Core.Extensions;
using _Tasks.Events;
using _Tasks.NewFeature.UI;

namespace _Tasks.NewFeature.Animation
{
    /// <summary>
    /// Centralized Animation Controller that handles all DOTween animations for game states
    /// Provides consistent animation methods with callbacks for state transitions
    /// </summary>
    public class AnimationController : MonoBehaviour
    {
        [Header("Settings")] [SerializeField] private AnimationSettings _animationSettings;
        [SerializeField] private GameEffectsSettings _gameEffectsSettings;

        [Header("References")] [SerializeField]
        private SFXManager _sfxManager;

        [Header("Debug")] [SerializeField] private bool _debugMode = false;


        private static AnimationController _instance;
        public static AnimationController Instance => _instance;

        #region Unity Lifecycle

        private void Awake()
        {
            // Singleton pattern
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
                return;
            }

            // Auto-find references if not assigned
            if (_sfxManager == null)
                _sfxManager = SFXManager.Instance;

            // Subscribe to events
            EventManager.Subscribe<MergeBlockEvent>(OnMergeBlockEvent);
        }

        private void OnDestroy()
        {
            // Unsubscribe from events
            EventManager.Unsubscribe<MergeBlockEvent>(OnMergeBlockEvent);
        }

        #endregion

        #region Block Movement Animation

        /// <summary>
        /// Animate single block movement with arrival effects
        /// </summary>
        public Tween MoveBlockAnimation(Transform blockTransform, Vector3 targetPosition, float duration,
            Action onComplete = null)
        {
            if (blockTransform == null)
            {
                Debug.LogWarning("[AnimationController] MoveBlockAnimation: blockTransform is null");
                onComplete?.Invoke();
                return null;
            }

            // Kill any existing animations on this transform to prevent conflicts
            blockTransform.DOKill();

            var sequence = DOTween.Sequence();

            // Main movement animation using basic ease
            sequence.Append(blockTransform.DOMove(targetPosition, duration)
                .SetEase(Ease.OutQuart));

            // Simple arrival effect without bounce (since BlockMovementConfig was removed)
            sequence.OnComplete(() =>
            {
                // Apply basic arrival effect
                if (blockTransform != null)
                {
                    // Use destination arrival animation for visual effects
                    DestinationArrivalAnimation(blockTransform, onComplete);
                }
                else
                {
                    onComplete?.Invoke();
                }
            });

            if (_debugMode)
                Debug.Log("[AnimationController] Block movement animation started");

            return sequence;
        }

        #endregion

        #region Block Merge Animation

        /// <summary>
        /// Animate block merging with tremor, merge, and spawn effects
        /// </summary>
        public void MergeBlocksAnimation(List<List<Cell>> mergeableGroups, GameController gameController,
            BlockFactory blockFactory, FoundationsController foundationsController, ScoreBox scoreBox,
            Action onComplete = null)
        {
            var config = _gameEffectsSettings?.MergeAnimation;

            // Safety check for null or empty groups
            if (mergeableGroups == null || mergeableGroups.Count == 0)
            {
                if (_debugMode)
                    Debug.LogWarning("[AnimationController] MergeBlocksAnimation called with null or empty groups");
                onComplete?.Invoke();
                return;
            }

            // Start the animation with a coroutine for better error handling
            StartCoroutine(SafeMergeBlocksAnimationCoroutine(mergeableGroups, gameController, blockFactory,
                foundationsController, scoreBox, onComplete));
        }

        /// <summary>
        /// Coroutine version of merge animation for safer execution
        /// </summary>
        private System.Collections.IEnumerator SafeMergeBlocksAnimationCoroutine(List<List<Cell>> mergeableGroups,
            GameController gameController,
            BlockFactory blockFactory, FoundationsController foundationsController, ScoreBox scoreBox,
            Action onComplete)
        {
            var sequence = DOTween.Sequence();

            foreach (var group in mergeableGroups)
            {
                var seedHash = new HashSet<BlockSeed>();
                var firstCell = group[0];
                var value = firstCell.Block.Value;
                var seed = firstCell.Block.Seed;
                seedHash.Add(seed);

                var groupSequence = DOTween.Sequence();
                var tremorSequence = DOTween.Sequence();

                // Store block references to prevent null reference issues
                var blocksToAnimate = new List<Block>();
                foreach (var cell in group)
                {
                    if (cell != null && cell.Block != null && IsBlockSafeToAnimate(cell.Block))
                    {
                        blocksToAnimate.Add(cell.Block);
                        seedHash.Add(cell.Block.Seed);
                    }
                }

                // Skip this group if no valid blocks found
                if (blocksToAnimate.Count == 0)
                {
                    if (_debugMode)
                        Debug.LogWarning("[AnimationController] Skipping group with no valid blocks");
                    continue;
                }

                // Safely cleanup all animations before starting tremor
                SafelyCleanupBlocksBeforeAnimation(blocksToAnimate);

                // Simplified tremor effect to avoid DOShakeScale issues
                foreach (var block in blocksToAnimate)
                {
                    if (IsBlockSafeToAnimate(block))
                    {
                        // Kill any existing animations to prevent conflicts
                        try
                        {
                            block.transform.DOKill(true);
                        }
                        catch (System.Exception e)
                        {
                            if (_debugMode)
                                Debug.LogWarning($"[AnimationController] Error killing animations: {e.Message}");
                            continue; // Skip this block if we can't clean up animations
                        }

                        // Simple scale pulse instead of shake to avoid DOShakeScale issues
                        var simpleTremorTween = block.transform
                            .DOScale(Vector3.one * 1.05f, _animationSettings.TremorDuration * 0.5f)
                            .SetEase(Ease.InOutQuart)
                            .SetLoops(2, LoopType.Yoyo)
                            .SetTarget(block.transform)
                            .OnUpdate(() =>
                            {
                                // Double-check object existence using our validation method
                                if (!IsBlockSafeToAnimate(block))
                                {
                                    tremorSequence.Kill();
                                    return;
                                }
                            });

                        tremorSequence.Join(simpleTremorTween);
                    }
                }

                groupSequence.Append(tremorSequence);

                // Animate blocks moving to merge center and scaling to zero
                for (var i = group.Count - 1; i > 0; i--)
                {
                    var cell = group[i];
                    var block = cell.Block;
                    var targetCell = group[i - 1];

                    if (!IsBlockSafeToAnimate(block) || targetCell == null) continue;

                    var targetPos = targetCell.transform.position;

                    var blockSequence = DOTween.Sequence();

                    // Kill any existing animations on this block to prevent conflicts
                    block.transform.DOKill();

                    // Enhanced merge movement with arc trajectory and rotation (with null checks)
                    var startPos = block.transform.position;
                    var midPoint = Vector3.Lerp(startPos, targetPos, 0.5f) + Vector3.up * 0.5f; // Arc trajectory

                    // Create path for curved movement
                    var path = new Vector3[] { startPos, midPoint, targetPos };

                    blockSequence.Append(block.transform
                        .DOPath(path, _animationSettings.MergeDuration, PathType.CatmullRom)
                        .SetEase(Ease.InOutQuart)
                        .SetTarget(block.transform)
                        .OnUpdate(() =>
                        {
                            if (!IsBlockSafeToAnimate(block))
                            {
                                blockSequence.Kill();
                                return;
                            }
                        }));

                    // Spinning rotation during merge
                    blockSequence.Join(block.transform
                        .DORotate(Vector3.forward * 360f, _animationSettings.MergeDuration, RotateMode.FastBeyond360)
                        .SetEase(Ease.InOutQuart)
                        .SetTarget(block.transform)
                        .OnUpdate(() =>
                        {
                            if (!IsBlockSafeToAnimate(block))
                            {
                                blockSequence.Kill();
                                return;
                            }
                        }));

                    // Dynamic scale animation (grow then shrink)
                    var scaleSequence = DOTween.Sequence();
                    scaleSequence.Append(block.transform
                        .DOScale(Vector3.one * 1.2f, _animationSettings.MergeDuration * 0.3f)
                        .SetEase(Ease.OutQuart)
                        .SetTarget(block.transform)
                        //Add sfx for play block state
                        //.OnStart(() => _sfxManager?.PlayOneShot(Consts.SFX_PlayBlock))
                        .OnUpdate(() =>
                        {
                            if (!IsBlockSafeToAnimate(block))
                            {
                                scaleSequence.Kill();
                                blockSequence.Kill();
                                return;
                            }
                        }));

                    scaleSequence.Append(block.transform.DOScale(Vector3.zero, _animationSettings.MergeDuration * 0.7f)
                        .SetEase(Ease.InBack)
                        .SetTarget(block.transform)
                        .OnUpdate(() =>
                        {
                            if (!IsBlockSafeToAnimate(block))
                            {
                                scaleSequence.Kill();
                                blockSequence.Kill();
                                return;
                            }
                        }));

                    blockSequence.Join(scaleSequence);
                    blockSequence.SetDelay(_animationSettings.BlockMoveDelay);

                    // Capture block reference for the callback
                    var blockToRemove = block;
                    blockSequence.OnComplete(() =>
                    {
                        //_sfxManager?.PlayOneShot(Consts.SFX_MergeBlocks);
                        targetCell.Block = null;

                        // Create floating score animation instead of direct score update
                        var scoreValue = mergeableGroups.Count * group.Count;
                        FloatingScoreToUIAnimation(targetPos, scoreValue, scoreBox, gameController);

                        // Safe cleanup before removing
                        if (blockToRemove != null)
                        {
                            SafelyKillBlockAnimations(blockToRemove);
                            blockToRemove.Remove();
                        }
                    });

                    groupSequence.Join(blockSequence);
                }

                // Enhanced final block transformation with implosion effect
                var finalSequence = DOTween.Sequence();
                var finalBlock = firstCell.Block;

                if (finalBlock != null && finalBlock.transform != null)
                {
                    // Kill any existing animations on the final block
                    finalBlock.transform.DOKill();

                    // Dramatic implosion effect with null checks
                    var implodeSequence = DOTween.Sequence();

                    // Quick scale up (energy gathering)
                    implodeSequence.Append(finalBlock.transform
                        .DOScale(Vector3.one * 1.3f, _animationSettings.MergeDuration * 0.2f)
                        .SetEase(Ease.OutQuart)
                        .SetTarget(finalBlock.transform)
                        .OnUpdate(() =>
                        {
                            if (finalBlock == null || finalBlock.transform == null)
                            {
                                implodeSequence.Kill();
                                return;
                            }
                        }));

                    // Rapid rotation during implosion
                    implodeSequence.Join(finalBlock.transform
                        .DORotate(Vector3.forward * 180f, _animationSettings.MergeDuration * 0.2f,
                            RotateMode.FastBeyond360)
                        .SetEase(Ease.OutQuart)
                        .SetTarget(finalBlock.transform)
                        .OnUpdate(() =>
                        {
                            if (finalBlock == null || finalBlock.transform == null)
                            {
                                implodeSequence.Kill();
                                return;
                            }
                        }));

                    // Final implosion to zero
                    implodeSequence.Append(finalBlock.transform
                        .DOScale(Vector3.zero, _animationSettings.MergeDuration * 0.8f)
                        .SetEase(Ease.InBack)
                        .SetTarget(finalBlock.transform)
                        .OnUpdate(() =>
                        {
                            if (finalBlock == null || finalBlock.transform == null)
                            {
                                implodeSequence.Kill();
                                return;
                            }
                        })
                        .OnComplete(() =>
                        {
                            if (finalBlock != null)
                            {
                                SafelyKillBlockAnimations(finalBlock);
                                finalBlock.Remove();
                                firstCell.Block = null;
                            }
                        }));

                    finalSequence.Append(implodeSequence);
                }

                groupSequence.Join(finalSequence);
                groupSequence.OnComplete(() =>
                {
                    var nextValue = value.Next(true);
                    var newBlock = blockFactory.Create(nextValue, seed);
                    firstCell.Block = newBlock;

                    // Dramatic emergence animation
                    newBlock.transform.localScale = Vector3.zero;
                    newBlock.transform.rotation = Quaternion.Euler(0, 0, 45f); // Start rotated

                    var emergenceSequence = DOTween.Sequence();

                    // Burst emergence with overshoot and null checks
                    emergenceSequence.Append(newBlock.transform
                        .DOScale(Vector3.one * 1.3f, _animationSettings.MergeDuration * 0.6f)
                        .SetEase(Ease.OutBack)
                        .SetTarget(newBlock.transform)
                        .OnUpdate(() =>
                        {
                            if (newBlock == null || newBlock.transform == null)
                            {
                                emergenceSequence.Kill();
                                return;
                            }
                        }));

                    // Rotation correction
                    emergenceSequence.Join(newBlock.transform
                        .DORotate(Vector3.zero, _animationSettings.MergeDuration * 0.6f)
                        .SetEase(Ease.OutBack)
                        .SetTarget(newBlock.transform)
                        .OnUpdate(() =>
                        {
                            if (newBlock == null || newBlock.transform == null)
                            {
                                emergenceSequence.Kill();
                                return;
                            }
                        }));

                    // Settle to final scale
                    emergenceSequence.Append(newBlock.transform
                        .DOScale(Vector3.one, _animationSettings.MergeDuration * 0.4f)
                        .SetEase(Ease.OutElastic)
                        .SetTarget(newBlock.transform)
                        .OnUpdate(() =>
                        {
                            if (newBlock == null || newBlock.transform == null)
                            {
                                emergenceSequence.Kill();
                                return;
                            }
                        }));

                    // Trigger destination arrival effect for the new block
                    emergenceSequence.OnComplete(() =>
                    {
                        if (newBlock != null && newBlock.transform != null)
                        {
                            DestinationArrivalAnimation(newBlock.transform, null, true); // Mark as from merge
                        }
                    });

                    foreach (var seedInGroup in seedHash)
                    {
                        var info = new BlockToFoundationInfo(seedInGroup, value, firstCell.Position);
                        if (foundationsController.TryAndAttach(info))
                        {
                            _sfxManager?.PlayOneShot(Consts.GetFoundationSFX(seedInGroup));

                            // Create floating score animation for foundation points
                            FloatingScoreToUIAnimation(firstCell.Position, Consts.FOUNDATION_POINTS, scoreBox,
                                gameController);
                        }
                    }
                });

                sequence.Append(groupSequence);
            }

            sequence.OnComplete(() =>
            {
                if (_debugMode)
                    Debug.Log("[AnimationController] Block merge animation completed");
                onComplete?.Invoke();
            });

            // Add error handling for the entire sequence
            sequence.OnKill(() =>
            {
                if (_debugMode)
                    Debug.Log(
                        "[AnimationController] Block merge animation was killed (likely due to object destruction)");
                onComplete?.Invoke();
            });

            sequence.Play();

            // Wait for the sequence to complete
            yield return sequence.WaitForCompletion();
        }

        #endregion

        #region Cell Spawn Animation

        /// <summary>
        /// Animate cell spawn with scale and alpha effects
        /// </summary>
        public void CellSpawnAnimation(List<Cell> cells, Action onComplete = null)
        {
            var config = _gameEffectsSettings?.CellSpawn;
            var sequence = DOTween.Sequence();

            foreach (var cell in cells)
            {
                if (cell != null)
                {
                    var cellSequence = DOTween.Sequence();

                    // Store original values
                    var originalScale = cell.transform.localScale;

                    // Set initial state
                    cell.transform.localScale = Vector3.zero;

                    // Animate scale
                    cellSequence.Append(cell.transform.DOScale(originalScale, config?.duration ?? 0.5f)
                        .SetEase(config?.scaleEase ?? Ease.OutBack));

                    sequence.Join(cellSequence);
                }
            }

            sequence.OnComplete(() =>
            {
                if (_debugMode)
                    Debug.Log("[AnimationController] Cell spawn animation completed");
                onComplete?.Invoke();
            });

            sequence.Play();
        }

        #endregion

        #region Block Spawn Animation

        /// <summary>
        /// Animate block spawn with scale effect
        /// </summary>
        public void BlockSpawnAnimation(Block block, Action onComplete = null)
        {
            if (block == null) return;

            block.transform.localScale = Vector3.zero;
            block.transform.DOScale(Vector3.one, _animationSettings.SpawnDuration)
                .SetEase(Ease.OutBack)
                .OnComplete(() =>
                {
                    if (_debugMode)
                        Debug.Log("[AnimationController] Block spawn animation completed");
                    onComplete?.Invoke();
                });
        }

        #endregion

        #region Helper Methods

        private List<Block> GetBlocksFromCells(List<Cell> cells)
        {
            var blocks = new List<Block>();
            foreach (var cell in cells)
            {
                if (cell.Block != null)
                    blocks.Add(cell.Block);
            }

            return blocks;
        }

        private List<Vector3> GetPositionsFromCells(List<Cell> cells)
        {
            var positions = new List<Vector3>();
            foreach (var cell in cells)
            {
                positions.Add(cell.Position);
            }

            return positions;
        }

        private List<Vector3> GetTargetPositions(List<Cell> cells, Board board)
        {
            var positions = new List<Vector3>();
            foreach (var cell in cells)
            {
                var targetCell = board.GetCell(cell.Coordinates.x, cell.Coordinates.y + 1);
                positions.Add(targetCell.Position);
            }

            return positions;
        }

        private void ApplyArrivalEffect(Block block, Vector3 position)
        {
            // Apply shake effect on arrival
            block.Visual.ShakeScale();

            // Trigger enhanced destination arrival animation
            DestinationArrivalAnimation(block.transform);
        }

        /// <summary>
        /// Calculate impact intensity based on block properties
        /// </summary>
        private float CalculateImpactIntensity(Block block)
        {
            if (block == null) return 0.5f; // Default intensity

            float intensity = 0.3f; // Base intensity

            // Increase intensity for special blocks
            if (block is ISpecialBlock)
            {
                intensity += 0.4f;
            }

            // Increase intensity based on block value (higher values = more impact)
            if (block.Value != null)
            {
                intensity += Mathf.Min((int)block.Value * 0.05f, 0.3f);
            }

            return Mathf.Clamp01(intensity);
        }

        /// <summary>
        /// Validate that a block is safe to animate
        /// </summary>
        private bool IsBlockSafeToAnimate(Block block)
        {
            return block != null &&
                   block.transform != null &&
                   block.gameObject != null &&
                   block.gameObject.activeInHierarchy &&
                   !block.transform.Equals(null); // Unity null check
        }

        /// <summary>
        /// Safely kill all animations on a block to prevent MissingReferenceException
        /// Call this before destroying blocks
        /// </summary>
        public void SafelyKillBlockAnimations(Block block)
        {
            if (!IsBlockSafeToAnimate(block)) return;

            try
            {
                // Kill all DOTween animations on this transform with complete cleanup
                block.transform.DOKill(true);

                // Also kill animations on child objects (like Visual components)
                if (block.Visual != null && block.Visual.transform != null)
                {
                    block.Visual.transform.DOKill(true);
                }

                // Kill any material animations (for glow effects)
                var renderer = block.GetComponent<Renderer>();
                if (renderer != null && renderer.material != null)
                {
                    renderer.material.DOKill(true);
                }

                if (_debugMode)
                    Debug.Log(
                        $"[AnimationController] Safely killed all animations for block at {block.transform.position}");
            }
            catch (System.Exception e)
            {
                if (_debugMode)
                    Debug.LogWarning($"[AnimationController] Exception while killing block animations: {e.Message}");
            }
        }

        /// <summary>
        /// Emergency cleanup - kills all active DOTween animations
        /// Use this as a last resort when experiencing animation-related crashes
        /// </summary>
        public void EmergencyKillAllAnimations()
        {
            try
            {
                DOTween.KillAll(true);
                if (_debugMode)
                    Debug.Log("[AnimationController] Emergency: Killed all DOTween animations");
            }
            catch (System.Exception e)
            {
                if (_debugMode)
                    Debug.LogError($"[AnimationController] Emergency cleanup failed: {e.Message}");
            }
        }

        /// <summary>
        /// Safely clean up all animations for a list of blocks before starting new animations
        /// </summary>
        private void SafelyCleanupBlocksBeforeAnimation(List<Block> blocks)
        {
            foreach (var block in blocks)
            {
                if (IsBlockSafeToAnimate(block))
                {
                    try
                    {
                        SafelyKillBlockAnimations(block);
                    }
                    catch (System.Exception e)
                    {
                        if (_debugMode)
                            Debug.LogWarning($"[AnimationController] Failed to cleanup block animations: {e.Message}");
                    }
                }
            }

            // Small delay to ensure cleanup is complete
            System.Threading.Thread.Sleep(1);
        }

        #endregion

        #region Enhanced Visual Effects

        /// <summary>
        /// Animate destination arrival with enhanced impact effects
        /// </summary>
        public void DestinationArrivalAnimation(Transform targetTransform, Action onComplete = null)
        {
            DestinationArrivalAnimation(targetTransform, onComplete, false);
        }

        /// <summary>
        /// Animate destination arrival with enhanced impact effects
        /// </summary>
        public void DestinationArrivalAnimation(Transform targetTransform, Action onComplete, bool isFromMerge)
        {
            if (targetTransform == null)
            {
                if (_debugMode)
                    Debug.LogWarning("[AnimationController] DestinationArrivalAnimation: targetTransform is null");
                onComplete?.Invoke();
                return;
            }

            var config = _gameEffectsSettings?.DestinationArrival;

            // Kill any existing animations on this transform to prevent conflicts
            targetTransform.DOKill();

            var sequence = DOTween.Sequence();

            // Determine if this is a special block
            var block = targetTransform.GetComponent<Block>();
            bool isSpecialBlock = block != null && block is ISpecialBlock;

            // Calculate impact intensity based on block properties
            float impactIntensity = CalculateImpactIntensity(block);

            // Broadcast arrival event for VFX system (no screen shake)
            EventManager.Broadcast(new BlockArrivalEvent
            {
                ArrivalPosition = targetTransform.position,
                ArrivedBlock = block,
                IsSpecialBlock = isSpecialBlock,
                ImpactIntensity = impactIntensity,
                TriggerScreenShake = false, // Removed screen shake for cleaner experience
                TriggerParticles = true,
                IsFromMerge = isFromMerge
            });

            // Enhanced multi-stage scale pulse effect
            if (config?.enableScalePulse == true)
            {
                var originalScale = targetTransform.localScale;
                float pulseScale = (config?.pulseScale ?? 1.2f) * (1f + impactIntensity * 0.4f); // More dramatic pulse

                // Multi-stage pulse for more impact
                sequence.Append(targetTransform.DOScale(originalScale * pulseScale,
                        (config?.pulseDuration ?? 0.3f) * 0.3f)
                    .SetEase(Ease.OutBack)
                    .OnUpdate(() =>
                    {
                        if (targetTransform == null)
                        {
                            sequence.Kill();
                            return;
                        }
                    }));

                // Quick overshoot for more dynamic feel
                sequence.Append(targetTransform.DOScale(originalScale * 0.9f,
                        (config?.pulseDuration ?? 0.3f) * 0.2f)
                    .SetEase(Ease.InQuart)
                    .OnUpdate(() =>
                    {
                        if (targetTransform == null)
                        {
                            sequence.Kill();
                            return;
                        }
                    }));

                // Final settle to normal scale
                sequence.Append(targetTransform.DOScale(Vector3.one,
                        (config?.pulseDuration ?? 0.3f) * 0.5f)
                    .SetEase(Ease.OutElastic)
                    .OnUpdate(() =>
                    {
                        if (targetTransform == null)
                        {
                            sequence.Kill();
                            return;
                        }
                    }));
            }

            // Enhanced multi-stage glow effect with dynamic color transitions
            if (config?.enableGlow == true)
            {
                var renderer = targetTransform.GetComponent<Renderer>();
                if (renderer != null)
                {
                    var material = renderer.material;
                    if (material != null)
                    {
                        var originalColor = material.color;
                        var glowColor = config?.glowColor ?? Color.cyan;

                        // Dynamic glow color based on intensity and block type
                        if (isSpecialBlock)
                        {
                            glowColor = Color.Lerp(glowColor, Color.yellow, 0.5f);
                        }

                        // Intensity-based brightness
                        glowColor = Color.Lerp(glowColor, Color.white, impactIntensity * 0.4f);

                        // Multi-stage glow: flash -> glow -> fade
                        var glowSequence = DOTween.Sequence();

                        // Quick flash
                        glowSequence.Append(material.DOColor(Color.white,
                                (config?.glowDuration ?? 0.5f) * 0.1f)
                            .SetEase(Ease.OutQuart)
                            .OnUpdate(() =>
                            {
                                if (material == null || targetTransform == null)
                                {
                                    sequence.Kill();
                                    return;
                                }
                            }));

                        // Sustained glow
                        glowSequence.Append(material.DOColor(glowColor,
                                (config?.glowDuration ?? 0.5f) * 0.4f)
                            .SetEase(Ease.OutQuart)
                            .OnUpdate(() =>
                            {
                                if (material == null || targetTransform == null)
                                {
                                    sequence.Kill();
                                    return;
                                }
                            }));

                        // Fade back to original
                        glowSequence.Append(material.DOColor(originalColor,
                                (config?.glowDuration ?? 0.5f) * 0.5f)
                            .SetEase(Ease.InQuart)
                            .OnUpdate(() =>
                            {
                                if (material == null || targetTransform == null)
                                {
                                    sequence.Kill();
                                    return;
                                }
                            }));

                        sequence.Join(glowSequence);
                    }
                }
            }

            sequence.OnComplete(() =>
            {
                if (_debugMode)
                    Debug.Log(
                        $"[AnimationController] Enhanced destination arrival animation completed - Intensity: {impactIntensity}");
                onComplete?.Invoke();
            });

            // Set the sequence to be automatically killed when the target is destroyed
            sequence.SetTarget(targetTransform);
            sequence.Play();
        }

        #endregion

        #region Score Animation

        /// <summary>
        /// Create floating score text animation that moves to score UI and triggers score update
        /// </summary>
        public void FloatingScoreToUIAnimation(Vector3 worldPosition, int scoreValue, ScoreBox scoreBox,
            GameController gameController, Action onComplete = null)
        {
            if (UIManager.Instance == null)
            {
                Debug.LogWarning("[AnimationController] UIManager.Instance is null!");
                onComplete?.Invoke();
                return;
            }

            var config = _gameEffectsSettings?.ScoreFeedback;

            // Create floating text UI element through UIManager
            var floatingTextUI =
                UIManager.Instance.CreateFloatingScoreUI(worldPosition, scoreValue, config?.floatingTextColor);
            if (floatingTextUI == null)
            {
                Debug.LogWarning("[AnimationController] Failed to create floating text UI!");
                onComplete?.Invoke();
                return;
            }

            // Get target position in UI space
            var targetUIPosition = UIManager.Instance.GetScoreBoxUIPosition(scoreBox);

            var sequence = DOTween.Sequence();

            // Animate movement to score UI (using cached RectTransform for UI elements)
            sequence.Append(floatingTextUI.RectTransform.DOLocalMove(targetUIPosition,
                config?.moveToScoreDuration ?? 1.2f).SetEase(config?.moveToScoreEase ?? Ease.InOutQuart));

            // Scale effect during movement
            sequence.Join(floatingTextUI.RectTransform.DOScale(Vector3.one * 0.8f,
                (config?.moveToScoreDuration ?? 1.2f) * 0.5f).SetEase(Ease.OutQuart));

            sequence.OnComplete(() =>
            {
                // Update score through GameController and trigger animated score update
                var currentScore = gameController.Score;
                gameController.Score = currentScore + scoreValue;

                // Use ScoreBox animated update instead of direct scale effect
                scoreBox.AnimatedScoreUpdate(gameController.Score, () =>
                {
                    if (_debugMode)
                        Debug.Log(
                            $"[AnimationController] Floating score animation completed - Added {scoreValue} points");

                    onComplete?.Invoke();
                });

                Destroy(floatingTextUI.gameObject);
            });

            sequence.Play();
        }

        #endregion


        #region Event Handlers

        /// <summary>
        /// Handle MergeBlockEvent by delegating to the existing MergeBlocksAnimation method
        /// </summary>
        private void OnMergeBlockEvent(MergeBlockEvent eventData)
        {
            if (_debugMode)
                Debug.Log("[AnimationController] Received MergeBlockEvent");

            // Find ScoreBox from the scene (since it's no longer passed as dependency)
            var scoreBox = FindObjectOfType<ScoreBox>();
            if (scoreBox == null)
            {
                Debug.LogWarning("[AnimationController] ScoreBox not found in scene for merge animation");
            }

            // Delegate to existing merge animation method
            MergeBlocksAnimation(
                eventData.MergeableGroups,
                eventData.GameController,
                eventData.BlockFactory,
                eventData.FoundationsController,
                scoreBox,
                eventData.OnComplete
            );
        }

        #endregion
    }
}