using UnityEngine;

namespace Whatwapp.Core.Utils.Executables
{
    public class LoadSceneExecutable : MonoBehaviour, IExecutable
    {
        [SerializeField] private string _sceneName;
        [SerializeField] private bool _additive;
        [SerializeField] private bool _unloadCurrentScene = true; // New option to control scene unloading
        [SerializeField] private bool _useTransitionSystem = true;
        [SerializeField] private float _transitionDuration = 1f;

        public void Execute()
        {
            Debug.Log($"[LoadSceneExecutable] Executing scene load: {_sceneName}, UseTransition: {_useTransitionSystem}, ServiceAvailable: {SceneTransitionServiceLocator.IsAvailable}");

            if (_useTransitionSystem && SceneTransitionServiceLocator.IsAvailable)
            {
                // Use the transition service through interface
                var transitionService = SceneTransitionServiceLocator.Instance;

                if (transitionService != null)
                {
                    if (_additive)
                    {
                        Debug.Log($"[LoadSceneExecutable] Loading scene additively: {_sceneName}, UnloadCurrent: {_unloadCurrentScene}");
                        transitionService.LoadSceneAdditive(_sceneName, _transitionDuration, _unloadCurrentScene);
                    }
                    else
                    {
                        Debug.Log($"[LoadSceneExecutable] Transitioning to scene: {_sceneName}");
                        transitionService.TransitionToScene(_sceneName, _transitionDuration);
                    }
                }
                else
                {
                    Debug.LogWarning("[LoadSceneExecutable] Transition service was null, using fallback");
                    FallbackSceneLoad();
                }
            }
            else
            {
                Debug.Log($"[LoadSceneExecutable] Using fallback scene loading for: {_sceneName}");
                FallbackSceneLoad();
            }
        }

        private void FallbackSceneLoad()
        {
            // Fallback to direct scene loading
            UnityEngine.SceneManagement.SceneManager.LoadScene(_sceneName,
                _additive ? UnityEngine.SceneManagement.LoadSceneMode.Additive :
                    UnityEngine.SceneManagement.LoadSceneMode.Single);
        }
    }
}