using UnityEngine;
using System.Linq;

namespace Whatwapp.Core.Utils
{
    /// <summary>
    /// Interface for scene transition services
    /// Provides abstraction for scene loading without assembly dependencies
    /// </summary>
    public interface ISceneTransitionService
    {
        /// <summary>
        /// Check if the transition service is available
        /// </summary>
        bool IsAvailable { get; }

        /// <summary>
        /// Check if a transition is currently in progress
        /// </summary>
        bool IsTransitioning { get; }

        /// <summary>
        /// Transition to a scene with default settings
        /// </summary>
        /// <param name="sceneName">Target scene name</param>
        void TransitionToScene(string sceneName);

        /// <summary>
        /// Transition to a scene with custom settings
        /// </summary>
        /// <param name="sceneName">Target scene name</param>
        /// <param name="duration">Transition duration</param>
        /// <param name="onComplete">Callback when transition completes</param>
        void TransitionToScene(string sceneName, float duration, System.Action onComplete = null);

        /// <summary>
        /// Load a scene additively with option to unload current scene
        /// </summary>
        /// <param name="sceneName">Scene to load additively</param>
        /// <param name="duration">Transition duration</param>
        /// <param name="unloadCurrent">Whether to unload the current scene after loading (default: false for overlays like pause menu)</param>
        /// <param name="onComplete">Callback when loading completes</param>
        void LoadSceneAdditive(string sceneName, float duration = 0.5f, bool unloadCurrent = false, System.Action onComplete = null);

        /// <summary>
        /// Unload a scene with transition effects
        /// </summary>
        /// <param name="sceneName">Scene to unload</param>
        /// <param name="duration">Transition duration</param>
        /// <param name="onComplete">Callback when unloading completes</param>
        void UnloadScene(string sceneName, float duration = 0.5f, System.Action onComplete = null);

        /// <summary>
        /// Force complete any ongoing transition (emergency use)
        /// </summary>
        void ForceCompleteTransition();
    }

    /// <summary>
    /// Static service locator for scene transition service
    /// Allows Core.Utils to access transition functionality without direct dependencies
    /// </summary>
    public static class SceneTransitionServiceLocator
    {
        private static ISceneTransitionService _instance;

        /// <summary>
        /// Get the current scene transition service instance
        /// </summary>
        public static ISceneTransitionService Instance
        {
            get
            {
                // Check if the instance is still valid (not destroyed)
                if (_instance != null && _instance is UnityEngine.MonoBehaviour mb && mb == null)
                {
                    // Instance was destroyed, clear the reference
                    _instance = null;
                }

                return _instance;
            }
        }

        /// <summary>
        /// Register a scene transition service implementation
        /// </summary>
        /// <param name="service">Service implementation to register</param>
        public static void Register(ISceneTransitionService service)
        {
            _instance = service;
            Debug.Log("[SceneTransitionServiceLocator] Service registered: " + service.GetType().Name);
        }

        /// <summary>
        /// Unregister the current service
        /// </summary>
        public static void Unregister()
        {
            if (_instance != null)
            {
                Debug.Log("[SceneTransitionServiceLocator] Service unregistered: " + _instance.GetType().Name);
                _instance = null;
            }
        }

        /// <summary>
        /// Check if a service is currently registered
        /// </summary>
        public static bool IsRegistered => _instance != null;

        /// <summary>
        /// Check if the service is available and ready to use
        /// </summary>
        public static bool IsAvailable
        {
            get
            {
                // Try to recover service if it was lost
                if (Instance == null)
                {
                    TryRecoverService();
                }

                return Instance != null && Instance.IsAvailable;
            }
        }

        /// <summary>
        /// Try to recover the service by finding an existing SceneTransitionService in the scene
        /// </summary>
        private static void TryRecoverService()
        {
            // Look for any SceneTransitionService in the scene
            var services = UnityEngine.Object.FindObjectsOfType<UnityEngine.MonoBehaviour>()
                .Where(mb => mb is ISceneTransitionService)
                .Cast<ISceneTransitionService>()
                .ToArray();

            if (services.Length > 0)
            {
                // Register the first available service
                Register(services[0]);
                Debug.Log("[SceneTransitionServiceLocator] Service recovered: " + services[0].GetType().Name);
            }
        }
    }
}
