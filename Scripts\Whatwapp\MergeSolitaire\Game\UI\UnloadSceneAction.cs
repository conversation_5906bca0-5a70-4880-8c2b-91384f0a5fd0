using UnityEngine;
using _Tasks.NewFeature.SceneManagement;

namespace Whatwapp.MergeSolitaire.Game.UI
{
    /// <summary>
    /// UI Action that uses SceneTransitionHelper for scene unloading
    /// This replaces the old UnloadSceneAction to use the new transition system
    /// </summary>
    public class UnloadSceneAction : MonoBehaviour
    {
        [Header("Scene Settings")]
        [SerializeField] private string _targetScene = "PauseMenu";
        [SerializeField] private float _transitionDuration = 0.5f;
        
        [Header("Predefined Scenes")]
        [SerializeField] private bool _usePredefinedScene = true;
        [SerializeField] private PredefinedScene _predefinedScene = PredefinedScene.PauseMenu;
        
        public enum PredefinedScene
        {
            PauseMenu
        }
        
        /// <summary>
        /// Execute the scene unload
        /// </summary>
        public void Execute()
        {
            if (_usePredefinedScene)
            {
                ExecutePredefinedUnload();
            }
            else
            {
                // Use custom scene name
                SceneTransitionHelper.UnloadSceneWithTransition(_targetScene, _transitionDuration, true);
            }
        }
        
        private void ExecutePredefinedUnload()
        {
            switch (_predefinedScene)
            {
                case PredefinedScene.PauseMenu:
                    SceneTransitionHelper.HidePauseMenu(_transitionDuration);
                    break;
            }
        }
        
        // Quick methods for UI buttons
        public void HidePauseMenu() => SceneTransitionHelper.HidePauseMenu(_transitionDuration);
    }
}
