using UnityEngine;

namespace Whatwapp.Core.Utils.Executables
{
    public class UnloadSceneExecutable : MonoBehaviour, IExecutable
    {
        [SerializeField] private string _sceneName;
        [SerializeField] private bool _useTransitionSystem = true;
        [SerializeField] private float _transitionDuration = 1f;

        public void Execute()
        {
            Debug.Log($"[UnloadSceneExecutable] Executing scene unload: {_sceneName}, UseTransition: {_useTransitionSystem}, ServiceAvailable: {SceneTransitionServiceLocator.IsAvailable}");

            if (_useTransitionSystem && SceneTransitionServiceLocator.IsAvailable)
            {
                // Use the transition service through interface
                var transitionService = SceneTransitionServiceLocator.Instance;

                if (transitionService != null)
                {
                    Debug.Log($"[UnloadSceneExecutable] Unloading scene with transition: {_sceneName}");
                    transitionService.UnloadScene(_sceneName, _transitionDuration);
                }
                else
                {
                    Debug.LogWarning("[UnloadSceneExecutable] Transition service was null, using fallback");
                    FallbackSceneUnload();
                }
            }
            else
            {
                Debug.Log($"[UnloadSceneExecutable] Using fallback scene unloading for: {_sceneName}");
                FallbackSceneUnload();
            }
        }

        private void FallbackSceneUnload()
        {
            // Fallback to direct scene unloading
            UnityEngine.SceneManagement.SceneManager.UnloadSceneAsync(_sceneName);
        }
    }
}